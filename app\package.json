{"name": "defineEditor", "productName": "Visual Define-XML Editor", "private": "true", "version": "1.1.16-current", "description": "Editor for CDISC Define-XML standard", "main": "./main.prod.js", "repository": {"type": "git", "url": "https://github.com/defineEditor/editor.git"}, "author": {"name": "<PERSON>", "email": "dmit<PERSON>.<EMAIL>", "url": ""}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": ""}], "scripts": {"electron-rebuild": "node -r babel-register ../internals/scripts/ElectronRebuild.js", "postinstall": "npm run electron-rebuild"}, "license": "AGPL-3.0-only", "dependencies": {}}